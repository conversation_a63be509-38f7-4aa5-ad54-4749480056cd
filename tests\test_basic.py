"""
Basic tests for base64_streaming_mcp package.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from src.config import MCPServerConfig, get_default_config, CompressionType, ImageFormat
from src.screen_capture import ScreenCapture, CaptureStats
from src.streaming import Base64Streamer, StreamState


class TestConfig:
    """Test configuration module."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = get_default_config()
        
        assert isinstance(config, MCPServerConfig)
        assert config.host == "localhost"
        assert config.port == 8765
        assert config.fps >= 1
        assert config.fps <= 60
        assert config.server_name == "base64-streaming-mcp"
        assert config.server_version == "1.0.0"
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Valid config
        config = MCPServerConfig(
            fps=30,
            jpeg_quality=85,
            port=8765
        )
        assert config.fps == 30
        assert config.jpeg_quality == 85
        
        # Invalid FPS
        with pytest.raises(ValueError):
            MCPServerConfig(fps=0)
        
        with pytest.raises(ValueError):
            MCPServerConfig(fps=100)
        
        # Invalid JPEG quality
        with pytest.raises(ValueError):
            MCPServerConfig(jpeg_quality=0)
        
        with pytest.raises(ValueError):
            MCPServerConfig(jpeg_quality=101)
    
    def test_compression_types(self):
        """Test compression type enum."""
        assert CompressionType.NONE == "none"
        assert CompressionType.GZIP == "gzip"
        assert CompressionType.ZSTD == "zstd"
    
    def test_image_formats(self):
        """Test image format enum."""
        assert ImageFormat.JPEG == "jpeg"
        assert ImageFormat.PNG == "png"
        assert ImageFormat.WEBP == "webp"


class TestScreenCapture:
    """Test screen capture module."""
    
    def test_capture_stats(self):
        """Test capture statistics."""
        stats = CaptureStats()
        
        assert stats.total_captures == 0
        assert stats.total_time == 0.0
        assert stats.avg_fps == 0.0
        assert stats.errors == 0
    
    @patch('src.screen_capture.mss.mss')
    def test_screen_capture_init(self, mock_mss):
        """Test screen capture initialization."""
        mock_sct = Mock()
        mock_sct.monitors = [
            {"left": 0, "top": 0, "width": 1920, "height": 1080},  # All in One
            {"left": 0, "top": 0, "width": 1920, "height": 1080}   # Primary monitor
        ]
        mock_mss.return_value = mock_sct
        
        config = get_default_config()
        capture = ScreenCapture(config)
        
        assert capture.config == config
        assert capture.monitor_count == 1
        assert isinstance(capture.stats, CaptureStats)
    
    @patch('src.screen_capture.mss.mss')
    def test_get_monitor_info(self, mock_mss):
        """Test monitor information retrieval."""
        mock_sct = Mock()
        mock_sct.monitors = [
            {"left": 0, "top": 0, "width": 1920, "height": 1080},  # All in One
            {"left": 0, "top": 0, "width": 1920, "height": 1080},  # Primary
            {"left": 1920, "top": 0, "width": 1920, "height": 1080}  # Secondary
        ]
        mock_mss.return_value = mock_sct
        
        config = get_default_config()
        capture = ScreenCapture(config)
        
        monitor_info = capture.get_monitor_info()
        
        assert len(monitor_info) == 2  # Excluding "All in One"
        assert monitor_info[0]["index"] == 0
        assert monitor_info[0]["primary"] == True
        assert monitor_info[1]["index"] == 1


class TestStreaming:
    """Test streaming module."""
    
    def test_stream_state_enum(self):
        """Test stream state enumeration."""
        assert StreamState.STOPPED == "stopped"
        assert StreamState.STARTING == "starting"
        assert StreamState.RUNNING == "running"
        assert StreamState.PAUSED == "paused"
        assert StreamState.ERROR == "error"
    
    @patch('src.streaming.ScreenCapture')
    def test_base64_streamer_init(self, mock_screen_capture):
        """Test Base64 streamer initialization."""
        config = get_default_config()
        streamer = Base64Streamer(config)
        
        assert streamer.config == config
        assert streamer.state == StreamState.STOPPED
        assert streamer._frame_counter == 0
        mock_screen_capture.assert_called_once_with(config)
    
    @patch('src.streaming.ScreenCapture')
    def test_streamer_compression_init(self, mock_screen_capture):
        """Test compression initialization."""
        # Test GZIP compression
        config = get_default_config()
        config.compression = CompressionType.GZIP
        streamer = Base64Streamer(config)
        assert streamer._compressor is None  # GZIP uses built-in
        
        # Test ZSTD compression
        config.compression = CompressionType.ZSTD
        with patch('src.streaming.zstd.ZstdCompressor') as mock_zstd:
            streamer = Base64Streamer(config)
            mock_zstd.assert_called_once_with(level=3)
    
    @patch('src.streaming.ScreenCapture')
    def test_compress_data(self, mock_screen_capture):
        """Test data compression."""
        config = get_default_config()
        
        # Test no compression
        config.compression = CompressionType.NONE
        streamer = Base64Streamer(config)
        
        test_data = b"test data"
        compressed, compression_type = streamer._compress_data(test_data)
        
        assert compressed == test_data
        assert compression_type == "none"
        
        # Test GZIP compression
        config.compression = CompressionType.GZIP
        streamer = Base64Streamer(config)
        
        compressed, compression_type = streamer._compress_data(test_data)
        
        assert len(compressed) > 0
        assert compression_type == "gzip"
        assert compressed != test_data  # Should be different due to compression


@pytest.mark.asyncio
class TestAsyncFunctions:
    """Test async functions."""
    
    @patch('src.streaming.ScreenCapture')
    async def test_streamer_start_stop(self, mock_screen_capture):
        """Test streamer start and stop."""
        config = get_default_config()
        streamer = Base64Streamer(config)
        
        # Mock screen capture
        mock_capture = Mock()
        mock_capture.start_continuous_capture = Mock(return_value=asyncio.coroutine(lambda: None)())
        mock_capture.stop_capture = Mock()
        streamer.screen_capture = mock_capture
        
        # Test start
        await streamer.start_stream()
        assert streamer.state == StreamState.RUNNING
        mock_capture.start_continuous_capture.assert_called_once()
        
        # Test stop
        await streamer.stop_stream()
        assert streamer.state == StreamState.STOPPED
        mock_capture.stop_capture.assert_called_once()
    
    @patch('src.streaming.ScreenCapture')
    async def test_streamer_pause_resume(self, mock_screen_capture):
        """Test streamer pause and resume."""
        config = get_default_config()
        streamer = Base64Streamer(config)
        
        # Start streaming first
        mock_capture = Mock()
        mock_capture.start_continuous_capture = Mock(return_value=asyncio.coroutine(lambda: None)())
        streamer.screen_capture = mock_capture
        
        await streamer.start_stream()
        assert streamer.state == StreamState.RUNNING
        
        # Test pause
        streamer.pause_stream()
        assert streamer.state == StreamState.PAUSED
        
        # Test resume
        streamer.resume_stream()
        assert streamer.state == StreamState.RUNNING


if __name__ == "__main__":
    pytest.main([__file__])
