"""
Main MCP WebSocket server for base64 screen streaming.
Implements Model Context Protocol with WebSocket transport.
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, Set
from datetime import datetime

import websockets
from websockets.server import WebSocketServerProtocol
import structlog

from mcp.server import Server
from mcp.types import (
    Resource, Tool, Prompt, Text<PERSON>ontent, ImageContent,
    CallToolResult, GetPromptResult,
    ListResourcesResult, ListToolsResult,
    ListPromptsResult, ReadResourceResult
)

from .config import MCPServerConfig, get_config
from .streaming import Base64Streamer, StreamState, StreamFrame
from .screen_capture import ScreenCapture

logger = structlog.get_logger(__name__)


class MCPScreenStreamingServer:
    """MCP Server for real-time screen streaming via WebSocket."""
    
    def __init__(self, config: Optional[MCPServerConfig] = None):
        """Initialize MCP server with configuration."""
        self.config = config or get_config()
        self.streamer = Base64Streamer(self.config)
        self.server = Server(self.config.server_name, self.config.server_version)
        
        # Connection management
        self.connections: Set[WebSocketServerProtocol] = set()
        self.active_streams: Dict[str, Base64Streamer] = {}
        
        # Setup MCP handlers
        self._setup_mcp_handlers()
        
        logger.info(
            "MCP Screen Streaming Server initialized",
            server_name=self.config.server_name,
            version=self.config.server_version,
            host=self.config.host,
            port=self.config.port
        )
    
    def _setup_mcp_handlers(self):
        """Setup MCP protocol handlers."""
        
        @self.server.list_resources()
        async def list_resources() -> ListResourcesResult:
            """List available resources."""
            resources = [
                Resource(
                    uri="screen://current",
                    name="Current Screen",
                    description="Current screen capture as base64 image",
                    mimeType="image/jpeg"
                ),
                Resource(
                    uri="stream://live",
                    name="Live Stream",
                    description="Live screen capture stream",
                    mimeType="application/json"
                ),
                Resource(
                    uri="info://stream",
                    name="Stream Information",
                    description="Current stream status and statistics",
                    mimeType="application/json"
                ),
                Resource(
                    uri="info://monitors",
                    name="Monitor Information",
                    description="Available monitors and their properties",
                    mimeType="application/json"
                )
            ]
            return ListResourcesResult(resources=resources)
        
        @self.server.read_resource()
        async def read_resource(uri: str) -> ReadResourceResult:
            """Read resource content."""
            try:
                if uri == "screen://current":
                    # Get single screen capture
                    frame_data = self.streamer.screen_capture.capture_screen()
                    if frame_data:
                        import base64
                        base64_data = base64.b64encode(frame_data).decode('utf-8')
                        content = ImageContent(
                            type="image",
                            data=base64_data,
                            mimeType=f"image/{self.config.image_format.value}"
                        )
                        return ReadResourceResult(contents=[content])
                    else:
                        return ReadResourceResult(contents=[
                            TextContent(type="text", text="Failed to capture screen")
                        ])
                
                elif uri == "stream://live":
                    # Get stream status
                    stream_info = self.streamer.get_stream_info()
                    content = TextContent(
                        type="text",
                        text=json.dumps(stream_info, indent=2)
                    )
                    return ReadResourceResult(contents=[content])
                
                elif uri == "info://stream":
                    # Get detailed stream information
                    stream_info = self.streamer.get_stream_info()
                    content = TextContent(
                        type="text",
                        text=json.dumps(stream_info, indent=2)
                    )
                    return ReadResourceResult(contents=[content])
                
                elif uri == "info://monitors":
                    # Get monitor information
                    monitor_info = self.streamer.screen_capture.get_monitor_info()
                    content = TextContent(
                        type="text",
                        text=json.dumps(monitor_info, indent=2)
                    )
                    return ReadResourceResult(contents=[content])
                
                else:
                    return ReadResourceResult(contents=[
                        TextContent(type="text", text=f"Unknown resource: {uri}")
                    ])
                    
            except Exception as e:
                logger.error("Error reading resource", uri=uri, error=str(e))
                return ReadResourceResult(contents=[
                    TextContent(type="text", text=f"Error reading resource: {str(e)}")
                ])
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            """List available tools."""
            tools = [
                Tool(
                    name="start_stream",
                    description="Start real-time screen streaming",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "fps": {"type": "integer", "minimum": 1, "maximum": 60, "default": 30},
                            "monitor_index": {"type": "integer", "minimum": 0, "default": 0},
                            "compression": {"type": "string", "enum": ["none", "gzip", "zstd"], "default": "gzip"}
                        }
                    }
                ),
                Tool(
                    name="stop_stream",
                    description="Stop screen streaming",
                    inputSchema={"type": "object", "properties": {}}
                ),
                Tool(
                    name="pause_stream",
                    description="Pause screen streaming",
                    inputSchema={"type": "object", "properties": {}}
                ),
                Tool(
                    name="resume_stream",
                    description="Resume screen streaming",
                    inputSchema={"type": "object", "properties": {}}
                ),
                Tool(
                    name="get_stream_status",
                    description="Get current stream status and statistics",
                    inputSchema={"type": "object", "properties": {}}
                ),
                Tool(
                    name="capture_screenshot",
                    description="Capture a single screenshot",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "monitor_index": {"type": "integer", "minimum": 0, "default": 0},
                            "format": {"type": "string", "enum": ["jpeg", "png", "webp"], "default": "jpeg"}
                        }
                    }
                ),
                Tool(
                    name="list_monitors",
                    description="List available monitors",
                    inputSchema={"type": "object", "properties": {}}
                ),
                Tool(
                    name="update_config",
                    description="Update streaming configuration",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "fps": {"type": "integer", "minimum": 1, "maximum": 60},
                            "compression": {"type": "string", "enum": ["none", "gzip", "zstd"]},
                            "image_format": {"type": "string", "enum": ["jpeg", "png", "webp"]},
                            "jpeg_quality": {"type": "integer", "minimum": 1, "maximum": 100},
                            "max_width": {"type": "integer", "minimum": 100},
                            "max_height": {"type": "integer", "minimum": 100}
                        }
                    }
                )
            ]
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """Handle tool calls."""
            try:
                if name == "start_stream":
                    return await self._handle_start_stream(arguments)
                elif name == "stop_stream":
                    return await self._handle_stop_stream(arguments)
                elif name == "pause_stream":
                    return await self._handle_pause_stream(arguments)
                elif name == "resume_stream":
                    return await self._handle_resume_stream(arguments)
                elif name == "get_stream_status":
                    return await self._handle_get_stream_status(arguments)
                elif name == "capture_screenshot":
                    return await self._handle_capture_screenshot(arguments)
                elif name == "list_monitors":
                    return await self._handle_list_monitors(arguments)
                elif name == "update_config":
                    return await self._handle_update_config(arguments)
                else:
                    return CallToolResult(
                        content=[TextContent(type="text", text=f"Unknown tool: {name}")]
                    )
                    
            except Exception as e:
                logger.error("Tool call failed", tool=name, error=str(e), exc_info=True)
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Tool call failed: {str(e)}")]
                )
        
        @self.server.list_prompts()
        async def list_prompts() -> ListPromptsResult:
            """List available prompts."""
            prompts = [
                Prompt(
                    name="stream_setup",
                    description="Setup screen streaming with optimal settings",
                    arguments=[
                        {"name": "use_case", "description": "Intended use case (presentation, gaming, monitoring)", "required": False},
                        {"name": "quality", "description": "Quality preference (low, medium, high)", "required": False}
                    ]
                ),
                Prompt(
                    name="troubleshoot_stream",
                    description="Troubleshoot streaming issues",
                    arguments=[
                        {"name": "issue", "description": "Description of the issue", "required": True}
                    ]
                )
            ]
            return ListPromptsResult(prompts=prompts)
        
        @self.server.get_prompt()
        async def get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
            """Get prompt content."""
            if name == "stream_setup":
                use_case = arguments.get("use_case", "general")
                quality = arguments.get("quality", "medium")
                
                content = f"""# Screen Streaming Setup

## Configuration for {use_case} use case with {quality} quality

### Recommended Settings:
"""
                if quality == "high":
                    content += """
- FPS: 30-60
- Compression: zstd
- Image Format: png
- JPEG Quality: 95
- Max Resolution: Full screen
"""
                elif quality == "low":
                    content += """
- FPS: 10-15
- Compression: gzip
- Image Format: jpeg
- JPEG Quality: 60
- Max Resolution: 1280x720
"""
                else:  # medium
                    content += """
- FPS: 20-30
- Compression: gzip
- Image Format: jpeg
- JPEG Quality: 85
- Max Resolution: 1920x1080
"""
                
                return GetPromptResult(
                    description=f"Setup guide for {use_case} streaming",
                    messages=[
                        {"role": "user", "content": {"type": "text", "text": content}}
                    ]
                )
            
            elif name == "troubleshoot_stream":
                issue = arguments.get("issue", "")
                content = f"""# Stream Troubleshooting

## Issue: {issue}

### Common Solutions:
1. Check if stream is running: use get_stream_status tool
2. Verify monitor availability: use list_monitors tool
3. Check system resources and memory usage
4. Try reducing FPS or image quality
5. Restart the stream if needed

### Diagnostic Steps:
1. Get current stream information
2. Check error logs
3. Verify configuration settings
4. Test with different compression settings
"""
                
                return GetPromptResult(
                    description="Troubleshooting guide for streaming issues",
                    messages=[
                        {"role": "user", "content": {"type": "text", "text": content}}
                    ]
                )
            
            else:
                return GetPromptResult(
                    description="Unknown prompt",
                    messages=[
                        {"role": "user", "content": {"type": "text", "text": f"Unknown prompt: {name}"}}
                    ]
                )

    # Tool handler methods
    async def _handle_start_stream(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle start_stream tool call."""
        try:
            # Update config with provided arguments
            if arguments:
                if "fps" in arguments:
                    self.config.fps = arguments["fps"]
                if "monitor_index" in arguments:
                    self.config.monitor_index = arguments["monitor_index"]
                if "compression" in arguments:
                    from .config import CompressionType
                    self.config.compression = CompressionType(arguments["compression"])

                # Update streamer config
                self.streamer.update_config(self.config)

            # Start streaming
            await self.streamer.start_stream()

            stream_info = self.streamer.get_stream_info()
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Stream started successfully!\n\nConfiguration:\n{json.dumps(stream_info['config'], indent=2)}"
                )]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to start stream: {str(e)}")]
            )

    async def _handle_stop_stream(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle stop_stream tool call."""
        try:
            await self.streamer.stop_stream()
            stats = self.streamer.get_stream_info()["stats"]

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Stream stopped successfully!\n\nFinal Statistics:\n{json.dumps(stats, indent=2)}"
                )]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to stop stream: {str(e)}")]
            )

    async def _handle_pause_stream(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle pause_stream tool call."""
        try:
            self.streamer.pause_stream()
            return CallToolResult(
                content=[TextContent(type="text", text="Stream paused successfully")]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to pause stream: {str(e)}")]
            )

    async def _handle_resume_stream(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle resume_stream tool call."""
        try:
            self.streamer.resume_stream()
            return CallToolResult(
                content=[TextContent(type="text", text="Stream resumed successfully")]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to resume stream: {str(e)}")]
            )

    async def _handle_get_stream_status(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle get_stream_status tool call."""
        try:
            stream_info = self.streamer.get_stream_info()
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Stream Status:\n{json.dumps(stream_info, indent=2)}"
                )]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to get stream status: {str(e)}")]
            )

    async def _handle_capture_screenshot(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle capture_screenshot tool call."""
        try:
            # Update monitor index if provided
            if "monitor_index" in arguments:
                self.config.monitor_index = arguments["monitor_index"]

            # Update format if provided
            if "format" in arguments:
                from .config import ImageFormat
                self.config.image_format = ImageFormat(arguments["format"])

            # Capture screenshot
            frame_data = self.streamer.screen_capture.capture_screen()
            if frame_data:
                import base64
                base64_data = base64.b64encode(frame_data).decode('utf-8')

                return CallToolResult(
                    content=[
                        TextContent(type="text", text="Screenshot captured successfully"),
                        ImageContent(
                            type="image",
                            data=base64_data,
                            mimeType=f"image/{self.config.image_format.value}"
                        )
                    ]
                )
            else:
                return CallToolResult(
                    content=[TextContent(type="text", text="Failed to capture screenshot")]
                )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to capture screenshot: {str(e)}")]
            )

    async def _handle_list_monitors(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle list_monitors tool call."""
        try:
            monitor_info = self.streamer.screen_capture.get_monitor_info()
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Available Monitors:\n{json.dumps(monitor_info, indent=2)}"
                )]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to list monitors: {str(e)}")]
            )

    async def _handle_update_config(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle update_config tool call."""
        try:
            # Update configuration
            updated_fields = []

            if "fps" in arguments:
                self.config.fps = arguments["fps"]
                updated_fields.append(f"FPS: {arguments['fps']}")

            if "compression" in arguments:
                from .config import CompressionType
                self.config.compression = CompressionType(arguments["compression"])
                updated_fields.append(f"Compression: {arguments['compression']}")

            if "image_format" in arguments:
                from .config import ImageFormat
                self.config.image_format = ImageFormat(arguments["image_format"])
                updated_fields.append(f"Image Format: {arguments['image_format']}")

            if "jpeg_quality" in arguments:
                self.config.jpeg_quality = arguments["jpeg_quality"]
                updated_fields.append(f"JPEG Quality: {arguments['jpeg_quality']}")

            if "max_width" in arguments:
                self.config.max_width = arguments["max_width"]
                updated_fields.append(f"Max Width: {arguments['max_width']}")

            if "max_height" in arguments:
                self.config.max_height = arguments["max_height"]
                updated_fields.append(f"Max Height: {arguments['max_height']}")

            # Update streamer config
            self.streamer.update_config(self.config)

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Configuration updated successfully!\n\nUpdated fields:\n" + "\n".join(updated_fields)
                )]
            )

        except Exception as e:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Failed to update configuration: {str(e)}")]
            )

    # WebSocket connection management
    async def handle_websocket_connection(self, websocket: WebSocketServerProtocol, path: str):
        """Handle new WebSocket connection."""
        client_id = str(uuid.uuid4())
        self.connections.add(websocket)

        logger.info(
            "New WebSocket connection",
            client_id=client_id,
            remote_address=websocket.remote_address,
            path=path
        )

        try:
            # Send welcome message
            welcome_msg = {
                "type": "welcome",
                "server": self.config.server_name,
                "version": self.config.server_version,
                "client_id": client_id,
                "timestamp": datetime.now().isoformat(),
                "capabilities": {
                    "streaming": True,
                    "screenshots": True,
                    "monitor_selection": True,
                    "compression": ["none", "gzip", "zstd"],
                    "formats": ["jpeg", "png", "webp"]
                }
            }
            await websocket.send(json.dumps(welcome_msg))

            # Handle messages
            async for message in websocket:
                try:
                    await self._handle_websocket_message(websocket, client_id, message)
                except Exception as e:
                    logger.error(
                        "Error handling WebSocket message",
                        client_id=client_id,
                        error=str(e),
                        exc_info=True
                    )

                    error_msg = {
                        "type": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send(json.dumps(error_msg))

        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed", client_id=client_id)
        except Exception as e:
            logger.error(
                "WebSocket connection error",
                client_id=client_id,
                error=str(e),
                exc_info=True
            )
        finally:
            self.connections.discard(websocket)
            # Clean up any active streams for this client
            if client_id in self.active_streams:
                await self.active_streams[client_id].stop_stream()
                del self.active_streams[client_id]

    async def _handle_websocket_message(self, websocket: WebSocketServerProtocol, client_id: str, message: str):
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            msg_type = data.get("type")

            if msg_type == "start_stream":
                await self._handle_ws_start_stream(websocket, client_id, data)
            elif msg_type == "stop_stream":
                await self._handle_ws_stop_stream(websocket, client_id, data)
            elif msg_type == "get_frame":
                await self._handle_ws_get_frame(websocket, client_id, data)
            elif msg_type == "get_status":
                await self._handle_ws_get_status(websocket, client_id, data)
            elif msg_type == "ping":
                await self._handle_ws_ping(websocket, client_id, data)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": f"Unknown message type: {msg_type}",
                    "timestamp": datetime.now().isoformat()
                }))

        except json.JSONDecodeError as e:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Invalid JSON: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }))

    async def _handle_ws_start_stream(self, websocket: WebSocketServerProtocol, client_id: str, data: Dict[str, Any]):
        """Handle WebSocket start_stream message."""
        try:
            # Create dedicated streamer for this client
            client_config = self.config.model_copy()

            # Update config with client preferences
            if "fps" in data:
                client_config.fps = data["fps"]
            if "compression" in data:
                from .config import CompressionType
                client_config.compression = CompressionType(data["compression"])
            if "format" in data:
                from .config import ImageFormat
                client_config.image_format = ImageFormat(data["format"])

            # Create and start streamer
            client_streamer = Base64Streamer(client_config)
            await client_streamer.start_stream()
            self.active_streams[client_id] = client_streamer

            # Send confirmation
            response = {
                "type": "stream_started",
                "client_id": client_id,
                "config": {
                    "fps": client_config.fps,
                    "compression": client_config.compression.value,
                    "format": client_config.image_format.value
                },
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(response))

            # Start streaming frames to this client
            asyncio.create_task(self._stream_frames_to_client(websocket, client_id))

        except Exception as e:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Failed to start stream: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }))

    async def _handle_ws_stop_stream(self, websocket: WebSocketServerProtocol, client_id: str, data: Dict[str, Any]):
        """Handle WebSocket stop_stream message."""
        try:
            if client_id in self.active_streams:
                await self.active_streams[client_id].stop_stream()
                del self.active_streams[client_id]

            response = {
                "type": "stream_stopped",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(response))

        except Exception as e:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Failed to stop stream: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }))

    async def _handle_ws_get_frame(self, websocket: WebSocketServerProtocol, client_id: str, data: Dict[str, Any]):
        """Handle WebSocket get_frame message (single frame request)."""
        try:
            frame_data = self.streamer.screen_capture.capture_screen()
            if frame_data:
                import base64
                base64_data = base64.b64encode(frame_data).decode('utf-8')

                response = {
                    "type": "frame",
                    "client_id": client_id,
                    "data": base64_data,
                    "format": self.config.image_format.value,
                    "timestamp": datetime.now().isoformat(),
                    "size": len(frame_data)
                }
                await websocket.send(json.dumps(response))
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Failed to capture frame",
                    "timestamp": datetime.now().isoformat()
                }))

        except Exception as e:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Failed to get frame: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }))

    async def _handle_ws_get_status(self, websocket: WebSocketServerProtocol, client_id: str, data: Dict[str, Any]):
        """Handle WebSocket get_status message."""
        try:
            stream_info = self.streamer.get_stream_info()

            response = {
                "type": "status",
                "client_id": client_id,
                "stream_info": stream_info,
                "active_streams": len(self.active_streams),
                "connections": len(self.connections),
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(response))

        except Exception as e:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Failed to get status: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }))

    async def _handle_ws_ping(self, websocket: WebSocketServerProtocol, client_id: str, data: Dict[str, Any]):
        """Handle WebSocket ping message."""
        response = {
            "type": "pong",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))

    async def _stream_frames_to_client(self, websocket: WebSocketServerProtocol, client_id: str):
        """Stream frames continuously to a specific client."""
        if client_id not in self.active_streams:
            return

        streamer = self.active_streams[client_id]

        try:
            async for frame in streamer.get_frame_stream():
                if client_id not in self.active_streams:
                    break

                if websocket.closed:
                    break

                # Send frame to client
                frame_msg = {
                    "type": "stream_frame",
                    "client_id": client_id,
                    "frame_id": frame.frame_id,
                    "timestamp": frame.timestamp,
                    "data": frame.data,
                    "format": frame.format,
                    "compression": frame.compression_type,
                    "size": frame.size_bytes,
                    "compressed_size": frame.compressed_size,
                    "metadata": frame.metadata
                }

                try:
                    await websocket.send(json.dumps(frame_msg))
                except websockets.exceptions.ConnectionClosed:
                    logger.info("Client disconnected during streaming", client_id=client_id)
                    break
                except Exception as e:
                    logger.error(
                        "Error sending frame to client",
                        client_id=client_id,
                        error=str(e)
                    )
                    break

        except Exception as e:
            logger.error(
                "Error in frame streaming",
                client_id=client_id,
                error=str(e),
                exc_info=True
            )
        finally:
            # Clean up
            if client_id in self.active_streams:
                await self.active_streams[client_id].stop_stream()
                del self.active_streams[client_id]

    async def start_server(self):
        """Start the WebSocket server."""
        logger.info(
            "Starting MCP Screen Streaming Server",
            host=self.config.host,
            port=self.config.port,
            max_connections=self.config.max_connections
        )

        # Start WebSocket server
        server = await websockets.serve(
            self.handle_websocket_connection,
            self.config.host,
            self.config.port,
            max_size=None,  # No message size limit
            max_queue=None,  # No queue size limit
            compression=None,  # Disable WebSocket compression (we handle our own)
            ping_interval=30,  # Send ping every 30 seconds
            ping_timeout=10,  # Wait 10 seconds for pong
        )

        logger.info(
            "MCP Screen Streaming Server started",
            host=self.config.host,
            port=self.config.port,
            server_name=self.config.server_name,
            version=self.config.server_version
        )

        return server

    async def run(self):
        """Run the server indefinitely."""
        server = await self.start_server()

        try:
            # Keep server running
            await server.wait_closed()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error("Server error", error=str(e), exc_info=True)
        finally:
            # Cleanup
            await self.shutdown()

    async def shutdown(self):
        """Shutdown the server and cleanup resources."""
        logger.info("Shutting down MCP Screen Streaming Server...")

        # Stop all active streams
        for client_id, streamer in list(self.active_streams.items()):
            try:
                await streamer.stop_stream()
            except Exception as e:
                logger.error(f"Error stopping stream for client {client_id}", error=str(e))

        self.active_streams.clear()

        # Close all connections
        if self.connections:
            await asyncio.gather(
                *[conn.close() for conn in self.connections],
                return_exceptions=True
            )
        self.connections.clear()

        # Stop main streamer
        try:
            await self.streamer.stop_stream()
        except Exception as e:
            logger.error("Error stopping main streamer", error=str(e))

        logger.info("Server shutdown complete")


# Main entry point
async def main():
    """Main entry point for the server."""
    import argparse

    parser = argparse.ArgumentParser(description="MCP Screen Streaming Server")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8765, help="Server port")
    parser.add_argument("--fps", type=int, default=30, help="Frames per second")
    parser.add_argument("--compression", choices=["none", "gzip", "zstd"], default="gzip", help="Compression type")
    parser.add_argument("--format", choices=["jpeg", "png", "webp"], default="jpeg", help="Image format")
    parser.add_argument("--quality", type=int, default=85, help="JPEG quality (1-100)")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--log-level", default="INFO", help="Logging level")

    args = parser.parse_args()

    # Setup logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Load configuration
    if args.config:
        from .config import load_config_from_file
        config = load_config_from_file(args.config)
    else:
        from .config import get_default_config
        config = get_default_config()

    # Override with command line arguments
    config.host = args.host
    config.port = args.port
    config.fps = args.fps
    config.jpeg_quality = args.quality
    config.log_level = args.log_level

    from .config import CompressionType, ImageFormat
    config.compression = CompressionType(args.compression)
    config.image_format = ImageFormat(args.format)

    # Create and run server
    server = MCPScreenStreamingServer(config)
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
