# Base64 Streaming MCP Server

A high-performance Model Context Protocol (MCP) server that provides real-time screen capture streaming in base64 format via WebSocket connections.

## Features

- 🖥️ **Cross-platform screen capture** (Windows, macOS, Linux)
- 🚀 **Real-time streaming** with configurable FPS (1-60)
- 📦 **Multiple compression options** (none, gzip, zstd)
- 🖼️ **Multiple image formats** (JPEG, PNG, WebP)
- 🔧 **Configurable quality and resolution**
- 📊 **Performance monitoring and statistics**
- 🔌 **WebSocket transport** for low-latency communication
- 🛡️ **MCP protocol compliance** with full feature support

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd base64_streaming_mcp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

### Basic Usage

1. Start the server:
```bash
python -m src.server
```

2. Connect via WebSocket:
```bash
# Default: ws://localhost:8765
```

3. Send commands via MCP protocol or WebSocket messages

### Configuration

Create a `config.json` file:
```json
{
  "host": "localhost",
  "port": 8765,
  "fps": 30,
  "compression": "gzip",
  "image_format": "jpeg",
  "jpeg_quality": 85,
  "max_width": 1920,
  "max_height": 1080
}
```

## MCP Protocol Support

### Resources

- `screen://current` - Current screen capture as base64 image
- `stream://live` - Live stream status and information
- `info://stream` - Detailed stream statistics
- `info://monitors` - Available monitors information

### Tools

- `start_stream` - Start real-time screen streaming
- `stop_stream` - Stop screen streaming
- `pause_stream` - Pause streaming
- `resume_stream` - Resume streaming
- `capture_screenshot` - Capture single screenshot
- `get_stream_status` - Get current status
- `list_monitors` - List available monitors
- `update_config` - Update configuration

### Prompts

- `stream_setup` - Setup streaming with optimal settings
- `troubleshoot_stream` - Troubleshoot streaming issues

## WebSocket API

### Connection

Connect to `ws://localhost:8765` (or your configured host/port)

### Message Format

All messages are JSON with the following structure:

```json
{
  "type": "message_type",
  "client_id": "uuid",
  "timestamp": "ISO8601",
  "data": "message_data"
}
```

### Message Types

#### Client to Server

**Start Stream:**
```json
{
  "type": "start_stream",
  "fps": 30,
  "compression": "gzip",
  "format": "jpeg"
}
```

**Stop Stream:**
```json
{
  "type": "stop_stream"
}
```

**Get Single Frame:**
```json
{
  "type": "get_frame"
}
```

**Get Status:**
```json
{
  "type": "get_status"
}
```

**Ping:**
```json
{
  "type": "ping"
}
```

#### Server to Client

**Welcome Message:**
```json
{
  "type": "welcome",
  "server": "base64-streaming-mcp",
  "version": "1.0.0",
  "client_id": "uuid",
  "capabilities": {...}
}
```

**Stream Frame:**
```json
{
  "type": "stream_frame",
  "frame_id": 123,
  "timestamp": 1234567890.123,
  "data": "base64_encoded_image",
  "format": "jpeg",
  "compression": "gzip",
  "size": 12345,
  "compressed_size": 8765,
  "metadata": {...}
}
```

**Status Response:**
```json
{
  "type": "status",
  "stream_info": {...},
  "active_streams": 2,
  "connections": 3
}
```

## Performance Optimization

### Recommended Settings

**High Quality (Presentations):**
- FPS: 30-60
- Compression: zstd
- Format: png
- Quality: 95

**Balanced (General Use):**
- FPS: 20-30
- Compression: gzip
- Format: jpeg
- Quality: 85

**Low Bandwidth (Remote Access):**
- FPS: 10-15
- Compression: gzip
- Format: jpeg
- Quality: 60
- Max Resolution: 1280x720

### Platform-Specific Optimizations

**Windows:**
- Enable threading
- Larger chunk sizes (128KB)

**macOS:**
- Lower FPS (25) for better performance
- Use zstd compression

**Linux:**
- Enable threading
- Use gzip compression

## Command Line Options

```bash
python -m src.server [OPTIONS]

Options:
  --host TEXT              Server host [default: localhost]
  --port INTEGER           Server port [default: 8765]
  --fps INTEGER            Frames per second [default: 30]
  --compression TEXT       Compression type [default: gzip]
  --format TEXT            Image format [default: jpeg]
  --quality INTEGER        JPEG quality 1-100 [default: 85]
  --config PATH            Configuration file path
  --log-level TEXT         Logging level [default: INFO]
```

## Examples

See the `examples/` directory for:
- `client_example.py` - Basic WebSocket client
- `mcp_client_example.py` - MCP protocol client
- `streaming_demo.html` - Web browser demo

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black src/
flake8 src/
mypy src/
```

## Troubleshooting

### Common Issues

1. **Permission Errors (macOS/Linux):**
   - Grant screen recording permissions
   - Run with appropriate privileges

2. **High CPU Usage:**
   - Reduce FPS
   - Lower image quality
   - Use more compression

3. **Memory Issues:**
   - Reduce buffer size
   - Lower resolution
   - Enable memory limits

4. **Connection Issues:**
   - Check firewall settings
   - Verify port availability
   - Check WebSocket support

### Logging

Enable debug logging:
```bash
python -m src.server --log-level DEBUG
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting guide
- Review the examples

## Changelog

### v1.0.0
- Initial release
- Cross-platform screen capture
- WebSocket streaming
- MCP protocol support
- Multiple compression options
